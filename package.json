{"name": "AIQuill-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@floating-ui/react-dom": "^2.1.4", "@google-cloud/translate": "^9.1.0", "@pinecone-database/pinecone": "^6.0.1", "@types/react-speech-recognition": "^3.9.6", "@vitalets/google-translate-api": "^9.2.1", "@wavesurfer/react": "^1.0.9", "axios": "^1.9.0", "critters": "^0.0.23", "date-fns": "^4.1.0", "framer-motion": "^12.11.3", "next": "15.1.7", "next-themes": "^0.4.4", "react": "^19.0.0", "react-animate-height": "^3.2.3", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.4.0", "react-markdown": "^10.1.0", "react-range": "^1.10.0", "react-select": "^5.10.1", "react-shiki": "^0.4.0", "react-speech-recognition": "^4.0.1", "react-syntax-highlighter": "^15.6.1", "react-type-animation": "^3.2.0", "toolcool-range-slider": "^4.0.8", "uuid": "^11.0.5", "wavesurfer.js": "^7.9.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/typography": "^0.5.16", "@types/mongodb": "^4.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}