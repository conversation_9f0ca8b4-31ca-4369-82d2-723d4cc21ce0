#!/usr/bin/env python3
"""
Test script to check if all service imports work correctly
"""

import sys
import os

# Add the backend directory to the Python path
backend_dir = os.path.join(os.path.dirname(__file__), 'python-fiass-backend')
sys.path.insert(0, backend_dir)

print("🧪 Testing service imports...")
print(f"Backend directory: {backend_dir}")

# Test direct imports from services directory
try:
    # Change to the backend directory
    os.chdir(backend_dir)

    # Test article processor
    try:
        from services.article_processor import process_article_url
        print("✅ Article processor imported successfully")
    except ImportError as e:
        print(f"❌ Article processor import failed: {e}")

    # Test YouTube processor
    try:
        from services.youtube_processor import process_youtube_url
        print("✅ YouTube processor imported successfully")
    except ImportError as e:
        print(f"❌ YouTube processor import failed: {e}")

    # Test PDF processor
    try:
        from services.pdf_processor import process_pdf_file
        print("✅ PDF processor imported successfully")
    except ImportError as e:
        print(f"❌ PDF processor import failed: {e}")

    # Test document processor
    try:
        from services.document_processor import process_document_file
        print("✅ Document processor imported successfully")
    except ImportError as e:
        print(f"❌ Document processor import failed: {e}")

    # Test audio processor
    try:
        from services.audio_proccessor import process_audio_file
        print("✅ Audio processor imported successfully")
    except ImportError as e:
        print(f"❌ Audio processor import failed: {e}")

except Exception as e:
    print(f"❌ Failed to change directory or import: {e}")

print("\n🔍 Testing individual dependencies...")

# Test beautifulsoup4
try:
    from bs4 import BeautifulSoup
    print("✅ BeautifulSoup4 available")
except ImportError:
    print("❌ BeautifulSoup4 not available")

# Test aiohttp
try:
    import aiohttp
    print("✅ aiohttp available")
except ImportError:
    print("❌ aiohttp not available")

# Test sentence transformers
try:
    from sentence_transformers import SentenceTransformer
    print("✅ sentence-transformers available")
except ImportError:
    print("❌ sentence-transformers not available")

print("\n✅ Import test completed!")
