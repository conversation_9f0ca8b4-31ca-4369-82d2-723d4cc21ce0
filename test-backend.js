// Simple test script to check backend connectivity
const axios = require('axios');

const BACKEND_URL = 'http://localhost:5010';

async function testBackend() {
    console.log('🧪 Testing backend connectivity...');
    
    try {
        // Test 1: Health check
        console.log('\n1. Testing health endpoint...');
        const healthResponse = await axios.get(`${BACKEND_URL}/api/health`, {
            timeout: 10000
        });
        console.log('✅ Health check successful:', healthResponse.data);
        
        // Test 2: Article processing
        console.log('\n2. Testing article processing...');
        const testUrl = 'https://whatrocks.github.io/c';
        const articleResponse = await axios.post(`${BACKEND_URL}/api/process_article`, {
            url: testUrl,
            index_name: 'default',
            client_email: ''
        }, {
            timeout: 30000
        });
        console.log('✅ Article processing response:', articleResponse.data);
        
    } catch (error) {
        console.error('❌ Backend test failed:');
        console.error('Error message:', error.message);
        console.error('Error code:', error.code);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

testBackend();
