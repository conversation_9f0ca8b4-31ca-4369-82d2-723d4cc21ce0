#!/usr/bin/env python3
"""
Simple script to start the backend server without debug mode
"""

import os
import sys

# Change to the backend directory
backend_dir = os.path.join(os.path.dirname(__file__), 'python-fiass-backend')
os.chdir(backend_dir)

# Add the backend directory to Python path
sys.path.insert(0, backend_dir)

# Import and run the main application
if __name__ == "__main__":
    # Read the full_code.py file and modify it to disable debug mode
    with open('full_code.py', 'r') as f:
        code = f.read()
    
    # Replace debug=True with debug=False
    code = code.replace('app.run(debug=True', 'app.run(debug=False')
    
    # Execute the modified code
    exec(code)
